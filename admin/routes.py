from flask import Blueprint, jsonify, session, request, redirect, url_for
from models import db, Admin

admin_bp = Blueprint("admin", __name__, template_folder="templates")

@admin_bp.route("/login", methods=["POST"])
def login():
    nric = request.form.get("nric").strip().upper()
    name = request.form.get("name").strip().upper()

    admin = Admin.query.filter_by(nric=nric).first()
    if not admin:
        admin = Admin(nric=nric, name=name)
        db.session.add(admin)
        db.session.commit()

    session["admin_id"] = admin.id
    return jsonify({"success": True, "message": "Welcome, admin!"})

@admin_bp.route("/logout", methods=["POST", "GET"])
def logout():
    session.pop("admin_id", None)
    return redirect(url_for("index"))