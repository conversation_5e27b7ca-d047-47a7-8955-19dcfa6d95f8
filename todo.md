# TODO
- Export events
- Admin credentials login in setup.html
- Fix button layout in dashboard
## Write tests
4. validate total is invariant by draw/send`
5. validate all routes.status_code==200
6. validate send event does not exceed corresponding draw

# 060725

# 050725
[x] Batch operations
[x] Select corresponding draw event when sending something
[x] Add NEW item to inventory during ops
[x] Display outstanding and completed orders

# 040725
[x] Learn databases again (theory)
[x] Implement db models
[x] api/draw 
[x] api/send 
[x] api/modify

# 010725
[x] Create transaction
[x] Initialise inventory