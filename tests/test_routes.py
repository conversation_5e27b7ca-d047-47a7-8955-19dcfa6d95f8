# import unittest
# from app import create_app

# class BaseTestCase(unittest.TestCase):
#     def setUp(self):
#         self.app = create_app()
#         self.client = self.app.test_client()
#         self.app_ctx = self.app.app_context()
#         self.ap_ctx.push()

#         db.create_all()

#     def tearDown(self):
#         self.ctx.pop()

#     def test_home(self):
#         response = self.client.get("/")
#         assert response.status_code == 200

# if __name__ == '__main__':
#     unittest.main()