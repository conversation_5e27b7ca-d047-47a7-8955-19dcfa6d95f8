from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate

db = SQLAlchemy()
migrate = Migrate()

class Admin(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    nric = db.Column(db.String(12), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    allocations = db.relationship('Stock', backref='admin', lazy=True)
    events = db.relationship("Event", backref="admin", lazy=True)

    def __repr__(self):
        return f"<Admin {self.name}>"
    
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    stock_id = db.Column(db.Integer, db.ForeignKey("stock.id"), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    events = db.relationship("Event", backref="user", lazy=True)

    def __repr__(self):
        return f"<User {self.name}>"

class Stock(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    description = db.Column(db.String(500), nullable=False)
    created_at = db.Column(db.DateTime, default=db.func.current_timestamp())
    admin_id = db.Column(db.Integer, db.ForeignKey("admin.id"), nullable=False)
    events = db.relationship("Event", backref="stock", lazy=True)
    items = db.relationship("Item", backref="stock", lazy=True)
    users = db.relationship("User", backref="stock", lazy=True)

    def __repr__(self):
        return f"<Stock {self.id}, created by {self.admin.name}>"

class Item(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    quantity = db.Column(db.Integer, nullable=False)
    on_loan = db.Column(db.Integer, default=0)
    stock_id = db.Column(db.Integer, db.ForeignKey("stock.id"), nullable=False)

    def __repr__(self):
        return f"<Item {self.name}>"
    
class Event(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    stock_id = db.Column(db.Integer, db.ForeignKey("stock.id"), nullable=False)
    items = db.Column(db.String(1000), nullable=False)
    remarks = db.Column(db.String(1000), nullable=True)
    event_type = db.Column(db.String(10), nullable=False) # "draw" or "send" or "modify" or "add"
    timestamp = db.Column(db.DateTime, default=db.func.current_timestamp())
    user_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=True) # either an admin or user event
    admin_id = db.Column(db.Integer, db.ForeignKey("admin.id"), nullable=True)

    def __repr__(self):
        return f"<Event {self.id}, {self.event_type} {self.items}>"