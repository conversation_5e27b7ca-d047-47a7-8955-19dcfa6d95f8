<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Store Inventory System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <!-- Admin Login Section -->
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card shadow-sm">
                    <div class="card-header text-center bg-primary text-white">
                        <h4 class="mb-0"><i class="bi bi-shield-lock"></i> Store Inventory System</h4>
                        <small class="text-white-50">Admin Login Required</small>
                    </div>
                    <div class="card-body">
                        <p class="text-center text-muted mb-4">Please login to access the inventory management system</p>
                        <form id="adminLoginForm" method="POST" action="/admin/login">
                            <div class="mb-3">
                                <label for="nric" class="form-label">NRIC</label>
                                <input type="text" class="form-control" id="nric" name="nric" required
                                       placeholder="Enter your NRIC">
                            </div>
                            <div class="mb-3">
                                <label for="name" class="form-label">Name</label>
                                <input type="text" class="form-control" id="name" name="name" required
                                       placeholder="Enter your name">
                            </div>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="bi bi-box-arrow-in-right"></i> Login as Admin
                                </button>
                            </div>
                        </form>
                        <div id="loginMessage" class="mt-3" style="display: none;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById('adminLoginForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const messageDiv = document.getElementById('loginMessage');
            const submitBtn = this.querySelector('button[type="submit"]');

            // Show loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Logging in...';

            fetch('/admin/login', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    messageDiv.className = 'alert alert-success mt-3';
                    messageDiv.innerHTML = '<i class="bi bi-check-circle"></i> ' + data.message;
                    messageDiv.style.display = 'block';

                    // Redirect after a short delay
                    setTimeout(() => {
                        window.location.href = '/setup';
                    }, 1500);
                } else {
                    messageDiv.className = 'alert alert-danger mt-3';
                    messageDiv.innerHTML = '<i class="bi bi-exclamation-triangle"></i> Login failed. Please try again.';
                    messageDiv.style.display = 'block';
                }
            })
            .catch(error => {
                messageDiv.className = 'alert alert-danger mt-3';
                messageDiv.innerHTML = '<i class="bi bi-exclamation-triangle"></i> An error occurred. Please try again.';
                messageDiv.style.display = 'block';
            })
            .finally(() => {
                // Reset button state
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="bi bi-box-arrow-in-right"></i> Login as Admin';
            });
        });
    </script>
</body>
</html>
