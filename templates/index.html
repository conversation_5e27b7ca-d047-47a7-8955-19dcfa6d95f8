<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Store Inventory System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6 text-center">
                <h1 class="mb-4">Store Inventory System</h1>
                <p class="mb-4">Manage your store inventory transactions</p>
                <div class="d-flex gap-3 justify-content-center">
                    <a href="/setup" class="btn btn-primary btn-lg">Create Transaction</a>
                    <!-- <a href="/dashboard" class="btn btn-outline-primary btn-lg">View Dashboard</a> -->
                </div>
            </div>
        </div>
    </div>
    <div class="container mt-6">
        <div class="row justify-content-center">
            <div class="col-md-6 text-center">
                <h1 class="mb-4">Admin Login</h1>
                <p class="mb-4">Login to manage users and transactions</p>
                <form method="POST" action="/admin/login">
                    <div class="mb-3">
                        <label for="nric" class="form-label mt-3">NRIC</label>
                        <input type="text" class="form-control" id="nric" name="nric" required>
                    </div>
                    <div class="mb-3">
                        <label for="name" class="form-label mt-3">Name</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <button type="submit" class="btn btn-primary">Login</button>
                </form>
            </div>
        </div>
    </div>
</body>
</html>
