<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Logs - Store Inventory</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .event-badge {
            font-size: 0.75rem;
        }
        .event-draw {
            background-color: #ffeaa7 !important;
        }
        .event-send {
            background-color: #74b9ff !important;
        }
        .event-modify {
            background-color: #fd79a8 !important;
        }
        .event-add {
            background-color: #00b894 !important;
        }
        .log-entry {
            border-left: 4px solid #dee2e6;
            transition: all 0.2s ease;
        }
        .log-entry:hover {
            background-color: #f8f9fa;
            border-left-color: #007bff;
        }
        .timestamp {
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2><i class="bi bi-journal-text"></i> Event Logs</h2>
                        <p class="text-muted mb-0">Stock: {{ stock.description }}</p>
                    </div>
                    <a href="{{ url_for('views.dashboard', stock_id=stock_id) }}" class="btn btn-outline-primary">
                        <i class="bi bi-arrow-left"></i> Back to Dashboard
                    </a>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">All Events</h5>
                        <small class="text-muted">
                            <span class="badge event-badge event-draw me-2">Draw</span>
                            <span class="badge event-badge event-send me-2">Send</span>
                            <span class="badge event-badge event-modify me-2">Modify</span>
                            <span class="badge event-badge event-add">Add</span>
                        </small>
                    </div>
                    <div class="card-body p-0">
                        {% if events %}
                            <div class="list-group list-group-flush">
                                {% for event in events %}
                                <div class="list-group-item log-entry">
                                    <div class="row align-items-center">
                                        <div class="col-md-2">
                                            <small class="timestamp text-muted">
                                                {{ (event.timestamp + timedelta(hours=8)).strftime('%Y-%m-%d') }}<br>
                                                {{ (event.timestamp + timedelta(hours=8)).strftime('%H:%M:%S') }}
                                            </small>
                                        </div>
                                        <div class="col-md-1">
                                            <span class="badge event-badge event-{{ event.event_type }}">
                                                {{ event.event_type.title() }}
                                            </span>
                                        </div>
                                        <div class="col-md-2">
                                            {% if event.user_id %}
                                                <strong>{{ user_dict.get(event.user_id, 'Unknown User') }}</strong>
                                            {% elif event.admin_id %}
                                                <strong class="text-primary">ADMIN {{ event.admin.name }}</strong>
                                            {% else %}
                                                <span class="text-muted">System</span>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-4">
                                            <div class="items-list">
                                                {% set items = event.items|from_json %}
                                                {% if items %}
                                                    {% for item_name, quantity in items.items() %}
                                                        <span class="badge bg-light text-dark me-1">
                                                            {{ item_name }}: {{ quantity }}
                                                        </span>
                                                    {% endfor %}
                                                {% else %}
                                                    <span class="text-muted">No items</span>
                                                {% endif %}
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            {% if event.remarks %}
                                                <small class="text-muted">
                                                    <i class="bi bi-chat-quote"></i> {{ event.remarks }}
                                                </small>
                                            {% else %}
                                                <small class="text-muted">No remarks</small>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="text-center py-5">
                                <div class="mb-3">
                                    <i class="bi bi-journal-x" style="font-size: 3rem; color: #6c757d;"></i>
                                </div>
                                <h5 class="text-muted">No events found</h5>
                                <p class="text-muted">No transactions have been recorded for this stock yet.</p>
                                <a href="{{ url_for('views.dashboard', stock_id=stock_id) }}" class="btn btn-primary">
                                    <i class="bi bi-arrow-left"></i> Back to Dashboard
                                </a>
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Summary Statistics -->
                {% if events %}
                <div class="row mt-4">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">{{ events|selectattr('event_type', 'equalto', 'draw')|list|length }}</h5>
                                <p class="card-text text-muted">Draw Events</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">{{ events|selectattr('event_type', 'equalto', 'send')|list|length }}</h5>
                                <p class="card-text text-muted">Send Events</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">{{ events|selectattr('event_type', 'equalto', 'modify')|list|length }}</h5>
                                <p class="card-text text-muted">Modify Events</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">{{ events|selectattr('event_type', 'equalto', 'add')|list|length }}</h5>
                                <p class="card-text text-muted">Add Events</p>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
