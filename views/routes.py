import logging
import json
from datetime import datetime, timedelta
from flask import Blueprint, render_template, request, flash, redirect, url_for, current_app, session
from wtforms.validators import NumberRange
from views.forms import SetupForm
from models import db, Admin, Stock, Item, Event, User
from api.routes import get_balance

views_bp = Blueprint("views", __name__, template_folder="templates")

@views_bp.route("/setup", methods=["GET", "POST"])
def setup():
    # Check if admin is logged in
    if "admin_id" not in session:
        flash("Please login as admin to access this page", "error")
        return redirect(url_for("index"))

    form = SetupForm()

    if form.validate_on_submit():
        items = []
        item_names = request.form.getlist("item_name")
        quantities = request.form.getlist("quantity")
        description = request.form.get("description")

        for name, qty in zip(item_names, quantities):
            if name.strip() and qty.strip():
                quantity = int(qty)  
                items.append({"name": name.strip(), "quantity": quantity})

        def upd_stock(admin_id, items, description):
            admin = Admin.query.filter_by(id=admin_id).first()
            if not admin:
                current_app.logger.error(f"Admin not found with id: {admin_id}")
                return False

            stock = Stock(admin_id=admin.id,
                          created_at=datetime.now(),
                          description=description)

            try:
                db.session.add(stock)
                db.session.commit()
            except Exception as e:
                current_app.logger.error(f"Error creating stock: {e}")
                return False

            for item in items:
                item = Item(name=item["name"],
                            quantity=item["quantity"],
                            stock_id=stock.id,
                            on_loan=0)
                
                try:
                    db.session.add(stock)
                    db.session.commit()
                except Exception as e:
                    current_app.logger.error(f"Error creating stock: {e}")
                    return False
                
                db.session.add(item)
                db.session.commit()   

            return stock.id

        stock_id = upd_stock(session["admin_id"], items, description)
        if stock_id:
            current_app.logger.info(f"Stock created with {len(items)} items")
            return redirect(url_for("views.dashboard", stock_id=stock_id))

    return render_template("setup.html", form=form)

@views_bp.route("/dashboard/<int:stock_id>", methods=["GET", "POST"])
def dashboard(stock_id):
    # Check if admin is logged in
    if "admin_id" not in session:
        flash("Please login as admin to access this page", "error")
        return redirect(url_for("index"))

    stock = Stock.query.filter_by(id=stock_id).first()

    items = []
    for item in stock.items:
        items.append({
            "name": item.name,
            "quantity": item.quantity,
            "on_loan": item.on_loan,
            "in_stock": item.quantity-item.on_loan,
            "total": item.quantity
        })

    # Calculate summary statistics
    total_items = len(items)
    total_in_stock = sum(item["in_stock"] for item in items)
    total_on_loan = sum(item["on_loan"] for item in items)

    # Get events
    draw_events = Event.query.filter_by(stock_id=stock_id, event_type="draw").all()
    send_events = Event.query.filter_by(stock_id=stock_id, event_type="send").all()
    modify_events = Event.query.filter_by(stock_id=stock_id, event_type="modify").all()

    # Get users
    users = User.query.filter_by(stock_id=stock_id).all()

    return render_template("dashboard.html",
                           inventory_items=items,
                           total_items=total_items,
                           total_in_stock=total_in_stock,
                           total_on_loan=total_on_loan,
                           stock_id=stock.id,
                           draw_events=draw_events, 
                           send_events=send_events,
                           modify_events=modify_events,
                           users=users)

@views_bp.route("/status/<int:stock_id>", methods=["GET", "POST"])
def status(stock_id):
    # Check if admin is logged in
    if "admin_id" not in session:
        flash("Please login as admin to access this page", "error")
        return redirect(url_for("index"))

    users = User.query.filter_by(stock_id=stock_id).all()
    events = {}
    balances = {}

    for user in users:
        events[user.name] = Event.query.filter_by(stock_id=stock_id, user_id=user.id).all()
        balances[user.name] = get_balance(user.id, stock_id)

    return render_template("status.html", users=users, events=events, balances=balances, stock_id=stock_id, timedelta=timedelta)

@views_bp.route("/logs/<int:stock_id>", methods=["GET"])
def logs(stock_id):
    """Display all events/logs for a specific stock"""
    # Check if admin is logged in
    if "admin_id" not in session:
        flash("Please login as admin to access this page", "error")
        return redirect(url_for("index"))

    stock = Stock.query.filter_by(id=stock_id).first()
    if not stock:
        flash("Stock not found", "error")
        return redirect(url_for("index"))

    # Get all events for this stock, ordered by timestamp (newest first)
    all_events = Event.query.filter_by(stock_id=stock_id).order_by(Event.timestamp.desc()).all()

    # Get all users for this stock for reference
    users = User.query.filter_by(stock_id=stock_id).all()
    user_dict = {user.id: user.name for user in users}

    return render_template("logs.html",
                         events=all_events,
                         stock=stock,
                         stock_id=stock_id,
                         user_dict=user_dict,
                         timedelta=timedelta)
